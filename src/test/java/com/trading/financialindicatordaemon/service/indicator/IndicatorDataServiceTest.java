package com.trading.financialindicatordaemon.service.indicator;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.client.indicatorapi.IndicatorData;
import com.trading.financialindicatordaemon.service.mining.DataMiningService;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import com.trading.financialindicatordaemon.service.yahoo.YahooFinanceService;
import com.trading.financialindicatordaemon.service.yahoo.YahooStockCandle;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class IndicatorDataServiceTest extends BaseTest {

    @Autowired
    private IndicatorDataService indicatorDataService;
    @Autowired
    private DataMiningService dataMiningService;
    @Autowired
    private YahooFinanceService yahooFinanceService;
    @Autowired
    private StockCandleDataService stockCandleDataService;

    @Test
    public void calculateForCrypto_shouldStore() {
        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), 2781);
        indicatorDataService.calculateForCrypto("SOL", "USD");
        List<IndicatorData> indicatorData = indicatorDataService.find("SOL", "USD");
        assertThat(indicatorData).isNotNull();
        assertThat(indicatorData).isNotEmpty();
        assertThat(indicatorData.size()).isEqualTo(1893);
    }

    @Test
    public void calculateForStock_shouldStore() {
        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles("TSLA", 1735430400, 1751155200);
        stockCandleDataService.insertYahoo(candles);

        indicatorDataService.calculateForStock("TSLA", "USD");
        List<IndicatorData> indicatorData = indicatorDataService.find("TSLA", "USD");
        assertThat(indicatorData).isNotNull();
        assertThat(indicatorData).isNotEmpty();
        assertThat(indicatorData.size()).isEqualTo(1893);
    }

    @Test
    public void insert_shouldUpdateExisting() {
        IndicatorData initial = new IndicatorData();
        initial.setTimestamp("2024-01-05T00:00:00Z");
        indicatorDataService.insert("SOL", "USD", List.of(initial));
        List<IndicatorData> indicatorData = indicatorDataService.find("SOL", "USD");
        assertThat(indicatorData.getFirst().getTimestamp()).isEqualTo("2024-01-05T00:00:00Z");

        IndicatorData updated = new IndicatorData();
        updated.setTimestamp("2024-01-06T00:00:00Z");

        indicatorDataService.insert("SOL", "USD", List.of(updated));
        assertThat(indicatorDataService.find("SOL", "USD").getFirst().getTimestamp()).isEqualTo("2024-01-06T00:00:00Z");
    }

}
