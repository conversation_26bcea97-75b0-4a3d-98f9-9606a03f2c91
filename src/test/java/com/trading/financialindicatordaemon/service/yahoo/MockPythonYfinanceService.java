package com.trading.financialindicatordaemon.service.yahoo;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Component
@Primary
public class MockPythonYfinanceService extends PythonYfinanceService {

    public List<YahooStockCandle> getStockCandles(String symbol, long startTimestamp, long endTimestamp) {
        if (1594598400 == startTimestamp && 1610150400 == endTimestamp || startTimestamp == 1735430400) {
            return List.of(
                    new YahooStockCandle("TSLA", LocalDateTime.now(), BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, 1, "USD"),
                    new YahooStockCandle("TSLA", LocalDateTime.now().minusDays(1), BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, BigDecimal.ONE, 1, "USD")
            );
        }

        return List.of();
    }

}
