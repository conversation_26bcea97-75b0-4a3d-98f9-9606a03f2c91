package com.trading.financialindicatordaemon.service.yahoo;

import com.trading.financialindicatordaemon.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class YahooFinanceServiceTest extends BaseTest {

    @Autowired
    private YahooFinanceService yahooFinanceService;

    @Test
    public void getStockCandles_shouldReturnCandles() {
        List<YahooStockCandle> candles = yahooFinanceService.getStockCandles("TSLA", 1735430400, 1751155200);
        assertThat(candles).isNotEmpty();
        assertThat(candles).hasSize(2);
    }

}
