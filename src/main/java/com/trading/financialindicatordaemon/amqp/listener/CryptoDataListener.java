package com.trading.financialindicatordaemon.amqp.listener;

import com.trading.financialindicatordaemon.amqp.publisher.RabbitMqPublisher;
import com.trading.financialindicatordaemon.config.RabbitMqConfig;
import com.trading.financialindicatordaemon.amqp.SymbolsMessage;
import com.trading.financialindicatordaemon.service.mining.DataMiningService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
public class CryptoDataListener extends BaseRabbitMqListener {

    private final RabbitMqPublisher rabbitMqPublisher;

    public CryptoDataListener(DataMiningService dataMiningService, RabbitMqPublisher rabbitMqPublisher) {
        super(dataMiningService);
        this.rabbitMqPublisher = rabbitMqPublisher;
    }

    @RabbitListener(queues = RabbitMqConfig.MINE_BTC_DATA_BY_SYMBOLS)
    public void handleMineBtcDataBySymbols(@Payload SymbolsMessage message, Channel channel,
                                           @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Message amqpMessage) {
        handleSymbolsMessage(message, "BTC", channel, deliveryTag, amqpMessage);
        rabbitMqPublisher.publishCreateIndicatorData();
    }

    @RabbitListener(queues = RabbitMqConfig.MINE_USD_DATA_BY_SYMBOLS)
    public void handleMineUsdDataBySymbols(@Payload SymbolsMessage message, Channel channel,
                                           @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Message amqpMessage) {
        handleSymbolsMessage(message, "USD", channel, deliveryTag, amqpMessage);
        rabbitMqPublisher.publishCreateIndicatorData();
    }
}

