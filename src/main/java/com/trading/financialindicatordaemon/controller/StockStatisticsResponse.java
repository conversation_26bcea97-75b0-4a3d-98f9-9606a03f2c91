package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.mapper.IndicatorDataWrapper;
import com.trading.financialindicatordaemon.service.stock.StockCandleData;

import java.util.List;

public class StockStatisticsResponse {
    private List<IndicatorDataWrapper> indicatorData;
    private List<StockCandleData> latestQuotes;

    public List<IndicatorDataWrapper> getIndicatorData() {
        return indicatorData;
    }

    public void setIndicatorData(List<IndicatorDataWrapper> indicatorData) {
        this.indicatorData = indicatorData;
    }

    public List<StockCandleData> getLatestQuotes() {
        return latestQuotes;
    }

    public void setLatestQuotes(List<StockCandleData> latestQuotes) {
        this.latestQuotes = latestQuotes;
    }
}
