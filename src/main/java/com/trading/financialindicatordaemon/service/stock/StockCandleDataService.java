package com.trading.financialindicatordaemon.service.stock;

import com.trading.financialindicatordaemon.mapper.StockCandleDataMapper;
import com.trading.financialindicatordaemon.mapper.StockCandleDataSymbolAndConversionCurrency;
import com.trading.financialindicatordaemon.service.yahoo.YahooStockCandle;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Component
public class StockCandleDataService {

    private final StockCandleDataMapper stockCandleDataMapper;

    public StockCandleDataService(StockCandleDataMapper stockCandleDataMapper) {
        this.stockCandleDataMapper = stockCandleDataMapper;
    }

    public void insertYahoo(List<YahooStockCandle> candles) {
        List<StockCandleData> candleDataList = candles
                .stream()
                .map(c -> {
                    StockCandleData stockCandleData = new StockCandleData();
                    stockCandleData.setSymbol(c.getSymbol());
                    stockCandleData.setTimestamp(c.getTimestamp().toLocalDate());
                    stockCandleData.setOpen(c.getOpen());
                    stockCandleData.setHigh(c.getHigh());
                    stockCandleData.setLow(c.getLow());
                    stockCandleData.setClose(c.getClose());
                    stockCandleData.setVolume(c.getVolume());
                    stockCandleData.setConversionCurrency(c.getConversionCurrency());
                    return stockCandleData;
                })
                .toList();

        insert(candleDataList);
    }

    public void insert(List<StockCandleData> candles) {
        stockCandleDataMapper.insert(candles);
    }

    public List<StockCandleData> find(String symbol, String conversionCurrency) {
        return stockCandleDataMapper.findBySymbolAndConversionCurrency(symbol, conversionCurrency);
    }

    public Optional<LocalDate> findLatestTimestamp(String symbol, String conversionCurrency) {
        return stockCandleDataMapper.findLatestTimestamp(symbol, conversionCurrency);
    }

    public List<StockCandleDataSymbolAndConversionCurrency> findAllSymbolAndConversionCurrency() {
        return stockCandleDataMapper.findAllSymbolAndConversionCurrency();
    }

    public List<StockCandleData> findLatestQuoteForAllSymbolsAndConversionCurrencies() {
        return stockCandleDataMapper.findLatestQuoteForAllSymbolsAndConversionCurrencies();
    }

}
