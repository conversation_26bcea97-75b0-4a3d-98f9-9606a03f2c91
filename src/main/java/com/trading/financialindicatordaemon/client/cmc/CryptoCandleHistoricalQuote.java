package com.trading.financialindicatordaemon.client.cmc;

import java.math.BigDecimal;

public record CryptoCandleHistoricalQuote(
    String symbol,
    String conversionCurrency,
    String timeOpen,
    String timeClose,
    String timeHigh,
    String timeLow,
    Quote quote
) {
    public record Quote(
        String name,
        BigDecimal open,
        BigDecimal high,
        BigDecimal low,
        BigDecimal close,
        BigDecimal volume,
        BigDecimal marketCap,
        String timestamp
    ) {}
}
