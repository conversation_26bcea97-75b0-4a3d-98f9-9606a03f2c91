package com.trading.financialindicatordaemon.client.indicatorapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;

public record IndicatorData(
    BigDecimal open,
    BigDecimal high,
    BigDecimal low,
    BigDecimal close,
    BigDecimal volume,
    BigDecimal marketCap,
    String timestamp,
    String name,
    BigDecimal hl2,
    @JsonProperty("smma_15") BigDecimal smma15,
    @JsonProperty("smma_19") BigDecimal smma19,
    @JsonProperty("smma_25") BigDecimal smma25,
    @JsonProperty("smma_29") BigDecimal smma29,
    Boolean p1,
    Boolean p2,
    Boolean p3,
    String color
) {}
