#!/usr/bin/env python3
"""
Yahoo Finance data fetcher using yfinance library with curl_cffi for better rate limiting.
This script can be called from Java to fetch stock data more reliably.
"""

import sys
import json
import argparse
from datetime import datetime, timedelta
import yfinance as yf


def fetch_stock_data(symbol, start_date, end_date, interval='1d'):
    """
    Fetch stock data using yfinance

    Args:
        symbol: Stock symbol (e.g., 'AAPL')
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        interval: Data interval (1d, 1h, etc.)

    Returns:
        JSON string with stock data or error
    """
    try:
        ticker = yf.Ticker(symbol)
        data = ticker.history(start=start_date, end=end_date, interval=interval)

        if data.empty:
            return json.dumps({'error': f'No data found for symbol {symbol}'})

        info = ticker.info
        currency = info['currency']

        result = []
        for date, row in data.iterrows():
            # Handle potential NaN values
            if any(pd.isna(val) for val in [row['Open'], row['High'], row['Low'], row['Close'], row['Volume']]):
                continue

            candle = {
                'symbol': symbol,
                'timestamp': int(date.timestamp()),
                'date': date.strftime('%Y-%m-%d'),
                'open': float(row['Open']),
                'high': float(row['High']),
                'low': float(row['Low']),
                'close': float(row['Close']),
                'adjClose': float(row['Close']),  # yfinance already provides adjusted close
                'volume': int(row['Volume']),
                'currency': currency
            }
            result.append(candle)

        return json.dumps({
            'success': True,
            'symbol': symbol,
            'currency': currency,
            'count': len(result),
            'data': result
        })

    except Exception as e:
        return json.dumps({
            'success': False,
            'error': str(e),
            'symbol': symbol
        })


def main():
    parser = argparse.ArgumentParser(description='Fetch Yahoo Finance data')
    parser.add_argument('symbol', help='Stock symbol (e.g., AAPL)')
    parser.add_argument('--start', required=True, help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end', required=True, help='End date (YYYY-MM-DD)')
    parser.add_argument('--interval', default='1d', help='Data interval (default: 1d)')

    args = parser.parse_args()

    result = fetch_stock_data(args.symbol, args.start, args.end, args.interval)
    print(result)


if __name__ == '__main__':
    # Import pandas here to handle NaN values
    import pandas as pd
    main()
