# Multi-stage build for Spring Boot application
FROM gradle:8.10.2-jdk17 AS build

# Set working directory
WORKDIR /app

# Copy Gradle wrapper and build files
COPY gradle/ gradle/
COPY gradlew .
COPY build.gradle .
COPY settings.gradle .

# Download dependencies (cached layer)
RUN ./gradlew dependencies --no-daemon

# Copy source code
COPY src/ src/

# Build the application
RUN ./gradlew bootJar --no-daemon

# Runtime stage
FROM eclipse-temurin:17-jre

# Install Python 3, pip, curl and other dependencies for Yahoo Finance integration
RUN apt-get update && apt-get install -y \
    curl \
    python3 \
    python3-pip \
    python3-venv \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Create app user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Copy the built JAR from build stage (includes Python resources)
COPY --from=build /app/build/libs/*.jar app.jar

# Create Python virtual environment with required packages
RUN python3 -m venv venv && \
    . venv/bin/activate && \
    pip install --no-cache-dir yfinance>=0.2.64 pandas>=1.5.0 requests>=2.28.0 curl-cffi>=0.5.0

# Change ownership to app user (including Python environment)
RUN chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose port 6601
EXPOSE 6601

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:6601/actuator/health || exit 1

# Run the application
ENTRYPOINT ["java", "-jar", "app.jar"]
